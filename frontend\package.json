{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "vercel-build": "npm run build"}, "dependencies": {"@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "antd": "^5.22.6", "axios": "^1.7.9", "dayjs": "^1.11.13", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.30.1", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.20", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}