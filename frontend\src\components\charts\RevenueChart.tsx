/**
 * Revenue Chart Component
 * Bar chart showing revenue trends
 */
import React from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card } from 'antd';

interface RevenueData {
  month: string;
  revenue: number;
  target: number;
}

interface RevenueChartProps {
  data?: RevenueData[];
  loading?: boolean;
}

// Sample data for demonstration
const defaultData: RevenueData[] = [
  { month: 'Jan', revenue: 45000, target: 50000 },
  { month: 'Feb', revenue: 52000, target: 50000 },
  { month: 'Mar', revenue: 48000, target: 55000 },
  { month: 'Apr', revenue: 61000, target: 55000 },
  { month: 'May', revenue: 55000, target: 60000 },
  { month: 'Jun', revenue: 67000, target: 60000 },
];

const RevenueChart: React.FC<RevenueChartProps> = ({ 
  data = defaultData, 
  loading = false 
}) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card 
      title="Revenue vs Target" 
      loading={loading}
      className="professional-card"
      role="region"
      aria-label="Revenue chart comparing actual revenue to targets"
    >
      <div style={{ width: '100%', height: 300 }}>
        <ResponsiveContainer>
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e5e5e5" />
            <XAxis 
              dataKey="month" 
              stroke="#737373"
              fontSize={12}
            />
            <YAxis 
              stroke="#737373"
              fontSize={12}
              tickFormatter={formatCurrency}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e5e5e5',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
              }}
              formatter={(value: number) => [formatCurrency(value), '']}
            />
            <Bar 
              dataKey="revenue" 
              fill="#3b82f6" 
              radius={[4, 4, 0, 0]}
              name="Actual Revenue"
            />
            <Bar 
              dataKey="target" 
              fill="#e5e5e5" 
              radius={[4, 4, 0, 0]}
              name="Target Revenue"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
      
      {/* Screen reader accessible data table */}
      <div className="sr-only">
        <table>
          <caption>Monthly revenue compared to targets</caption>
          <thead>
            <tr>
              <th>Month</th>
              <th>Actual Revenue</th>
              <th>Target Revenue</th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr key={index}>
                <td>{item.month}</td>
                <td>{formatCurrency(item.revenue)}</td>
                <td>{formatCurrency(item.target)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
};

export default RevenueChart;
