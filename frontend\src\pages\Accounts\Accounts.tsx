import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  DatePicker,
  Select,
  Row,
  Col,
  Card,
  Typography,
  Spin,
  Modal,
  message,
  Input,
  Popconfirm,
  Tooltip
} from 'antd';
import { 
  EditOutlined, 
  DeleteOutlined, 
  FilterOutlined, 
  PlusOutlined,
  DollarOutlined,
  BellOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { apiService } from '../../api';
import type { CustomerAccount } from '../../api';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import ErrorToast from '../../components/ErrorDisplay/ErrorToast';
import AddAccountModal from './AddAccountModal';
import EditAccountModal from './EditAccountModal';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { Search } = Input;

const Accounts: React.FC = () => {
  const [accounts, setAccounts] = useState<CustomerAccount[]>([]);
  const [filteredAccounts, setFilteredAccounts] = useState<CustomerAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [searchText, setSearchText] = useState<string>('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<CustomerAccount | null>(null);

  // Use our custom error handler hook
  const { errorMessage, showError, clearError } = useErrorHandler();

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'paid', label: 'Paid' },
    { value: 'outstanding', label: 'Outstanding' },
    { value: 'overdue', label: 'Overdue' }
  ];

  const getPaymentStatus = (outstanding: number, reminderDate?: string) => {
    if (outstanding === 0) {
      return { status: 'Paid', color: 'success', icon: <CheckCircleOutlined />, value: 'paid' };
    } else if (reminderDate && dayjs().isAfter(dayjs(reminderDate))) {
      return { status: 'Overdue', color: 'error', icon: <BellOutlined />, value: 'overdue' };
    } else if (outstanding > 0) {
      return { status: 'Outstanding', color: 'warning', icon: <ExclamationCircleOutlined />, value: 'outstanding' };
    }
    return { status: 'Unknown', color: 'default', icon: null, value: 'unknown' };
  };

  const isOverdue = (reminderDate: string | null) => {
    if (!reminderDate) return false;
    return dayjs().isAfter(dayjs(reminderDate));
  };

  const columns = [
    {
      title: 'Invoice',
      dataIndex: 'invoice_number',
      key: 'invoice_number',
      width: 120,
      sorter: (a: CustomerAccount, b: CustomerAccount) => a.invoice_number.localeCompare(b.invoice_number),
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date: string) => dayjs(date).format('MMM DD, YYYY'),
      sorter: (a: CustomerAccount, b: CustomerAccount) => dayjs(a.date).unix() - dayjs(b.date).unix(),
    },
    {
      title: 'Customer',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 180,
      render: (name: string) => name || '-',
      sorter: (a: CustomerAccount, b: CustomerAccount) => (a.customer_name || '').localeCompare(b.customer_name || ''),
    },
    {
      title: 'Project',
      dataIndex: 'project_name',
      key: 'project_name',
      width: 150,
      render: (projectName: string) => projectName || '-',
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      sorter: (a: CustomerAccount, b: CustomerAccount) => a.name.localeCompare(b.name),
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount: number) => `$${amount.toLocaleString()}`,
      sorter: (a: CustomerAccount, b: CustomerAccount) => a.amount - b.amount,
    },
    {
      title: 'Outstanding',
      dataIndex: 'outstanding',
      key: 'outstanding',
      width: 140,
      render: (outstanding: number, record: CustomerAccount) => {
        const { status, color, icon } = getPaymentStatus(outstanding, record.reminder_date);
        return (
          <Space>
            <span>${outstanding.toLocaleString()}</span>
            <Tag color={color} icon={icon}>
              {status}
            </Tag>
          </Space>
        );
      },
      sorter: (a: CustomerAccount, b: CustomerAccount) => a.outstanding - b.outstanding,
    },
    {
      title: 'Reminder',
      dataIndex: 'reminder_date',
      key: 'reminder_date',
      width: 120,
      render: (reminderDate: string | null) => {
        if (!reminderDate) return '-';
        const overdue = isOverdue(reminderDate);
        return (
          <Space>
            <span className={overdue ? 'text-red-500' : ''}>
              {dayjs(reminderDate).format('MMM DD, YYYY')}
            </span>
            {overdue && (
              <Tooltip title="Overdue">
                <BellOutlined className="text-red-500" />
              </Tooltip>
            )}
          </Space>
        );
      },
    },
    {
      title: 'Comments',
      dataIndex: 'comments',
      key: 'comments',
      width: 200,
      render: (comments: string) => (
        <div className="truncate max-w-xs" title={comments}>
          {comments || '-'}
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_: any, record: CustomerAccount) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEdit(record)}
          />
          <Popconfirm
            title="Are you sure you want to delete this account?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              size="small"
              danger
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const fetchAccounts = async () => {
    setLoading(true);
    try {
      const accountsData = await apiService.getAccounts();
      setAccounts(accountsData);
      setFilteredAccounts(accountsData);
    } catch (error: any) {
      showError(error.message || 'Failed to fetch accounts');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAccounts();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [accounts, dateRange, statusFilter, searchText]);

  const applyFilters = () => {
    let filtered = [...accounts];

    // Apply search filter
    if (searchText) {
      filtered = filtered.filter(account =>
        account.invoice_number.toLowerCase().includes(searchText.toLowerCase()) ||
        account.name.toLowerCase().includes(searchText.toLowerCase()) ||
        (account.customer_name && account.customer_name.toLowerCase().includes(searchText.toLowerCase())) ||
        (account.project_name && account.project_name.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    // Apply date range filter
    if (dateRange) {
      const [startDate, endDate] = dateRange;
      filtered = filtered.filter(account => {
        const accountDate = dayjs(account.date);
        return accountDate.isAfter(startDate.subtract(1, 'day')) && 
               accountDate.isBefore(endDate.add(1, 'day'));
      });
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(account => {
        const { value } = getPaymentStatus(account.outstanding, account.reminder_date);
        return value === statusFilter;
      });
    }

    setFilteredAccounts(filtered);
  };

  const handleEdit = (account: CustomerAccount) => {
    setSelectedAccount(account);
    setShowEditModal(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await apiService.deleteAccount(id);
      message.success('Account deleted successfully');
      fetchAccounts();
    } catch (error: any) {
      message.error(error.message || 'Failed to delete account');
      showError(error.message || 'Failed to delete account');
    }
  };

  const handleAddAccount = () => {
    setShowAddModal(true);
  };

  const handleAddSuccess = () => {
    setShowAddModal(false);
    fetchAccounts();
  };

  const handleEditSuccess = () => {
    setShowEditModal(false);
    setSelectedAccount(null);
    fetchAccounts();
  };

  const clearFilters = () => {
    setDateRange(null);
    setStatusFilter('');
    setSearchText('');
  };

  // Calculate summary statistics
  const totalAmount = filteredAccounts.reduce((sum, account) => sum + account.amount, 0);
  const totalOutstanding = filteredAccounts.reduce((sum, account) => sum + account.outstanding, 0);
  const totalPaid = totalAmount - totalOutstanding;
  const overdueCount = filteredAccounts.filter(account => 
    account.reminder_date && isOverdue(account.reminder_date)
  ).length;

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>Account Management</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddAccount}
        >
          Add Invoice
        </Button>
      </div>

      {/* Error Display */}
      <ErrorToast message={errorMessage} onClose={clearError} />

      {/* Summary Cards */}
      <Row gutter={16} className="mb-4">
        <Col span={6}>
          <Card size="small">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">${totalAmount.toLocaleString()}</div>
              <div className="text-gray-500">Total Amount</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">${totalOutstanding.toLocaleString()}</div>
              <div className="text-gray-500">Outstanding</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">${totalPaid.toLocaleString()}</div>
              <div className="text-gray-500">Paid</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{overdueCount}</div>
              <div className="text-gray-500">Overdue</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card className="mb-4">
        <Row gutter={16} align="middle">
          <Col span={6}>
            <div className="mb-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <Search
                placeholder="Search by Invoice, Name, Customer, or Project"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </div>
          </Col>
          <Col span={6}>
            <div className="mb-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date Range
              </label>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
                placeholder={['Start Date', 'End Date']}
              />
            </div>
          </Col>
          <Col span={4}>
            <div className="mb-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <Select
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: '100%' }}
                placeholder="Select status"
              >
                {statusOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col span={4}>
            <div className="mb-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                &nbsp;
              </label>
              <Space>
                <Button
                  icon={<FilterOutlined />}
                  onClick={applyFilters}
                >
                  Apply
                </Button>
                <Button onClick={clearFilters}>
                  Clear
                </Button>
              </Space>
            </div>
          </Col>
          <Col span={4}>
            <div className="text-right">
              <div className="text-sm text-gray-500">
                Showing {filteredAccounts.length} of {accounts.length} invoices
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* Accounts Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredAccounts}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} invoices`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {accounts.length === 0 && !loading && (
        <Card>
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <DollarOutlined className="text-4xl" />
            </div>
            <Title level={4} className="text-gray-500">No invoices found</Title>
            <p className="text-gray-400">
              Create your first invoice to get started with account management.
            </p>
          </div>
        </Card>
      )}

      {/* Add Account Modal */}
      <AddAccountModal
        visible={showAddModal}
        onCancel={() => setShowAddModal(false)}
        onSuccess={handleAddSuccess}
      />

      {/* Edit Account Modal */}
      {selectedAccount && (
        <EditAccountModal
          visible={showEditModal}
          account={selectedAccount}
          onCancel={() => {
            setShowEditModal(false);
            setSelectedAccount(null);
          }}
          onSuccess={handleEditSuccess}
        />
      )}
    </div>
  );
};

export default Accounts;
