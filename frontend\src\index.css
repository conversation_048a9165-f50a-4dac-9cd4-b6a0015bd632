@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Professional Design System */
@import './styles/design-system.css';

/* Accessibility - Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better keyboard navigation */
.focus-visible:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* Ensure modal inputs maintain focus properly */
.ant-modal .ant-input:focus,
.ant-modal .ant-select-focused .ant-select-selector,
.ant-modal .ant-input-number:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  outline: none !important;
}

/* Prevent focus loss on modal inputs */
.ant-modal .ant-input,
.ant-modal .ant-select-selector,
.ant-modal .ant-input-number {
  transition: border-color 0.3s, box-shadow 0.3s;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .ant-btn {
    border-width: 2px;
  }

  .ant-card {
    border-width: 2px;
  }
}
