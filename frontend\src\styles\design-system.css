/**
 * Professional Design System
 * WCAG AA compliant colors and consistent spacing
 */

:root {
  /* Primary Brand Colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Semantic Colors */
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  
  --color-info-500: #06b6d4;
  --color-info-600: #0891b2;
  --color-info-700: #0e7490;

  /* Neutral Colors */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;

  /* Typography Scale */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  /* Spacing Scale */
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */

  /* Border Radius */
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --color-neutral-50: #171717;
    --color-neutral-100: #262626;
    --color-neutral-200: #404040;
    --color-neutral-300: #525252;
    --color-neutral-400: #737373;
    --color-neutral-500: #a3a3a3;
    --color-neutral-600: #d4d4d4;
    --color-neutral-700: #e5e5e5;
    --color-neutral-800: #f5f5f5;
    --color-neutral-900: #fafafa;
  }
}

/* Professional Component Styles */
.professional-card {
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-base);
}

.professional-card:hover {
  box-shadow: var(--shadow-md);
}

.professional-button {
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
}

.professional-button-primary {
  background-color: var(--color-primary-600);
  color: white;
  border-color: var(--color-primary-600);
}

.professional-button-primary:hover {
  background-color: var(--color-primary-700);
  border-color: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.professional-button-secondary {
  background-color: transparent;
  color: var(--color-primary-600);
  border-color: var(--color-primary-600);
}

.professional-button-secondary:hover {
  background-color: var(--color-primary-50);
  color: var(--color-primary-700);
  border-color: var(--color-primary-700);
}

.professional-input {
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.professional-input:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
  outline: none;
}

/* Status Indicators */
.status-success {
  color: var(--color-success-700);
  background-color: var(--color-success-50);
  border: 1px solid var(--color-success-200);
}

.status-warning {
  color: var(--color-warning-700);
  background-color: var(--color-warning-50);
  border: 1px solid var(--color-warning-200);
}

.status-error {
  color: var(--color-error-700);
  background-color: var(--color-error-50);
  border: 1px solid var(--color-error-200);
}

.status-info {
  color: var(--color-info-700);
  background-color: var(--color-info-50);
  border: 1px solid var(--color-info-200);
}

/* Typography */
.text-heading-1 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  line-height: 1.2;
  color: var(--color-neutral-900);
}

.text-heading-2 {
  font-size: var(--font-size-3xl);
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-neutral-900);
}

.text-heading-3 {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  line-height: 1.4;
  color: var(--color-neutral-800);
}

.text-body {
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-neutral-700);
}

.text-small {
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--color-neutral-600);
}

/* Layout Utilities */
.container-professional {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.grid-professional {
  display: grid;
  gap: var(--spacing-6);
}

.flex-professional {
  display: flex;
  gap: var(--spacing-4);
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-professional {
    padding: 0 var(--spacing-4);
  }
  
  .grid-professional {
    gap: var(--spacing-4);
  }
  
  .flex-professional {
    gap: var(--spacing-3);
  }
}
