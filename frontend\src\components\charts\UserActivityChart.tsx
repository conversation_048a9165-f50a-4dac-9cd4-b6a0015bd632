/**
 * User Activity Chart Component
 * Professional data visualization with accessibility
 */
import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card } from 'antd';

interface UserActivityData {
  date: string;
  activeUsers: number;
  newUsers: number;
}

interface UserActivityChartProps {
  data?: UserActivityData[];
  loading?: boolean;
}

// Sample data for demonstration
const defaultData: UserActivityData[] = [
  { date: '2024-01-01', activeUsers: 45, newUsers: 5 },
  { date: '2024-01-02', activeUsers: 52, newUsers: 8 },
  { date: '2024-01-03', activeUsers: 48, newUsers: 3 },
  { date: '2024-01-04', activeUsers: 61, newUsers: 12 },
  { date: '2024-01-05', activeUsers: 55, newUsers: 7 },
  { date: '2024-01-06', activeUsers: 67, newUsers: 15 },
  { date: '2024-01-07', activeUsers: 59, newUsers: 9 },
];

const UserActivityChart: React.FC<UserActivityChartProps> = ({ 
  data = defaultData, 
  loading = false 
}) => {
  return (
    <Card 
      title="User Activity Trends" 
      loading={loading}
      className="professional-card"
      role="region"
      aria-label="User activity chart showing trends over time"
    >
      <div style={{ width: '100%', height: 300 }}>
        <ResponsiveContainer>
          <LineChart
            data={data}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e5e5e5" />
            <XAxis 
              dataKey="date" 
              stroke="#737373"
              fontSize={12}
              tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            />
            <YAxis 
              stroke="#737373"
              fontSize={12}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e5e5e5',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
              }}
              labelFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <Line 
              type="monotone" 
              dataKey="activeUsers" 
              stroke="#3b82f6" 
              strokeWidth={3}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
              name="Active Users"
            />
            <Line 
              type="monotone" 
              dataKey="newUsers" 
              stroke="#22c55e" 
              strokeWidth={3}
              dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#22c55e', strokeWidth: 2 }}
              name="New Users"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      
      {/* Screen reader accessible data table */}
      <div className="sr-only">
        <table>
          <caption>User activity data over time</caption>
          <thead>
            <tr>
              <th>Date</th>
              <th>Active Users</th>
              <th>New Users</th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr key={index}>
                <td>{new Date(item.date).toLocaleDateString()}</td>
                <td>{item.activeUsers}</td>
                <td>{item.newUsers}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
};

export default UserActivityChart;
